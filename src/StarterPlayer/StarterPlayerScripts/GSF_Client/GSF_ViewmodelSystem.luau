--!strict
--[[
	Gun System Framework - Viewmodel System
	
	This module manages the FPS viewmodel for R6 blocky characters:
	- Creates and manages R6 arms viewmodel
	- Positions weapons in first-person view
	- Handles viewmodel animations and positioning
	- Manages weapon attachment and detachment
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- VIEWMODEL SYSTEM
-- ============================================================================

local ViewmodelSystem = {}
ViewmodelSystem.__index = ViewmodelSystem

-- Viewmodel configuration for R6 blocky characters
local VIEWMODEL_CONFIG = {
	-- Arm positioning
	armOffset = CFrame.new(0.5, -0.5, -2), -- Offset from camera
	armSize = Vector3.new(1, 2, 1), -- R6 arm dimensions
	armColor = Color3.fromRGB(255, 204, 153), -- Default skin color

	-- Weapon positioning
	weaponOffset = CFrame.new(0.2, -0.3, -1.5),
	weaponRotation = CFrame.Angles(0, math.rad(5), 0),

	-- Animation settings
	swayIntensity = 0.02,
	bobIntensity = 0.01,
	aimTransitionTime = 0.3,
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function ViewmodelSystem.new(): ViewmodelSystem
	local self = setmetatable({}, ViewmodelSystem)

	-- State
	self.character = nil
	self.camera = Workspace.CurrentCamera :: Camera
	self.viewmodel = nil
	self.currentWeapon = nil
	self.isAiming = false

	-- Arms
	self.leftArm = nil
	self.rightArm = nil
	self.weaponModel = nil

	-- Animation state
	self.swayOffset = CFrame.new()
	self.bobOffset = CFrame.new()
	self.aimOffset = CFrame.new()
	self.baseOffset = VIEWMODEL_CONFIG.armOffset

	-- Connections
	self.connections = {}

	-- Initialize
	self:_initialize()

	return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function ViewmodelSystem:_initialize(): ()
	-- Create viewmodel folder
	self.viewmodel = Instance.new("Folder")
	self.viewmodel.Name = "Viewmodel"
	self.viewmodel.Parent = self.camera

	-- Create arms
	self:_createArms()

	-- Start update loop
	self.connections.heartbeat = RunService.Heartbeat:Connect(function(deltaTime)
		self:_update(deltaTime)
	end)
end

function ViewmodelSystem:_createArms(): ()
	-- Create left arm
	self.leftArm = Instance.new("Part")
	self.leftArm.Name = "LeftArm"
	self.leftArm.Size = VIEWMODEL_CONFIG.armSize
	self.leftArm.Color = VIEWMODEL_CONFIG.armColor
	self.leftArm.Material = Enum.Material.SmoothPlastic
	self.leftArm.CanCollide = false
	self.leftArm.Anchored = true
	self.leftArm.Parent = self.viewmodel

	-- Create right arm
	self.rightArm = Instance.new("Part")
	self.rightArm.Name = "RightArm"
	self.rightArm.Size = VIEWMODEL_CONFIG.armSize
	self.rightArm.Color = VIEWMODEL_CONFIG.armColor
	self.rightArm.Material = Enum.Material.SmoothPlastic
	self.rightArm.CanCollide = false
	self.rightArm.Anchored = true
	self.rightArm.Parent = self.viewmodel

	-- Add basic arm details
	self:_addArmDetails()
end

function ViewmodelSystem:_addArmDetails(): ()
	-- Add simple hand details for R6 blocky style
	local function createHand(arm: Part): Part
		local hand = Instance.new("Part")
		hand.Name = "Hand"
		hand.Size = Vector3.new(1, 0.4, 1)
		hand.Color = arm.Color
		hand.Material = arm.Material
		hand.CanCollide = false
		hand.Anchored = true
		hand.Parent = arm

		-- Position hand at end of arm
		local weld = Instance.new("WeldConstraint")
		weld.Part0 = arm
		weld.Part1 = hand
		weld.Parent = arm

		return hand
	end

	-- Create hands
	createHand(self.leftArm)
	createHand(self.rightArm)
end

-- ============================================================================
-- WEAPON MANAGEMENT
-- ============================================================================

function ViewmodelSystem:equipWeapon(weapon: any): ()
	self.currentWeapon = weapon

	-- Create weapon model in viewmodel
	self:_createWeaponModel(weapon)

	-- Position arms for weapon
	self:_positionArmsForWeapon()
end

function ViewmodelSystem:unequipWeapon(): ()
	if self.weaponModel then
		self.weaponModel:Destroy()
		self.weaponModel = nil
	end

	self.currentWeapon = nil

	-- Reset arm positions
	self:_resetArmPositions()
end

function ViewmodelSystem:_createWeaponModel(weapon: any): ()
	-- Create a simple weapon model for demonstration
	-- In a real implementation, this would load the actual weapon model
	self.weaponModel = Instance.new("Part")
	self.weaponModel.Name = weapon.name or "Weapon"
	self.weaponModel.Size = Vector3.new(0.2, 0.2, 2) -- Simple rifle shape
	self.weaponModel.Color = Color3.fromRGB(64, 64, 64)
	self.weaponModel.Material = Enum.Material.Metal
	self.weaponModel.CanCollide = false
	self.weaponModel.Anchored = true
	self.weaponModel.Parent = self.viewmodel

	-- Add weapon details
	local barrel = Instance.new("Part")
	barrel.Name = "Barrel"
	barrel.Size = Vector3.new(0.1, 0.1, 1)
	barrel.Color = Color3.fromRGB(32, 32, 32)
	barrel.Material = Enum.Material.Metal
	barrel.CanCollide = false
	barrel.Anchored = true
	barrel.Parent = self.weaponModel
end

function ViewmodelSystem:_positionArmsForWeapon(): ()
	if not self.currentWeapon then
		return
	end

	-- Position arms to hold weapon
	-- This would be customized per weapon type
	local leftArmOffset = CFrame.new(-0.5, 0, -0.5) * CFrame.Angles(0, math.rad(-15), math.rad(-10))
	local rightArmOffset = CFrame.new(0.5, 0, -0.3) * CFrame.Angles(0, math.rad(10), math.rad(5))

	self.leftArmOffset = leftArmOffset
	self.rightArmOffset = rightArmOffset
end

function ViewmodelSystem:_resetArmPositions(): ()
	self.leftArmOffset = CFrame.new()
	self.rightArmOffset = CFrame.new()
end

-- ============================================================================
-- ANIMATION AND POSITIONING
-- ============================================================================

function ViewmodelSystem:_update(deltaTime: number): ()
	if not self.camera then
		return
	end

	-- Update sway and bob
	self:_updateSway(deltaTime)
	self:_updateBob(deltaTime)

	-- Update viewmodel position
	self:_updateViewmodelPosition()
end

function ViewmodelSystem:_updateSway(deltaTime: number): ()
	-- Simple mouse-based sway
	local mouse = Players.LocalPlayer:GetMouse()
	local screenCenter = Vector2.new(self.camera.ViewportSize.X / 2, self.camera.ViewportSize.Y / 2)
	local mouseOffset = Vector2.new(mouse.X - screenCenter.X, mouse.Y - screenCenter.Y)

	-- Convert to sway
	local swayX = mouseOffset.X / screenCenter.X * VIEWMODEL_CONFIG.swayIntensity
	local swayY = mouseOffset.Y / screenCenter.Y * VIEWMODEL_CONFIG.swayIntensity

	self.swayOffset = CFrame.new(swayX, swayY, 0)
end

function ViewmodelSystem:_updateBob(deltaTime: number): ()
	-- Simple walking bob effect
	local character = Players.LocalPlayer.Character
	if not character or not character:FindFirstChild("Humanoid") then
		return
	end

	local humanoid = character.Humanoid
	local walkSpeed = humanoid.WalkSpeed
	local moveVector = humanoid.MoveDirection

	if moveVector.Magnitude > 0.1 then
		local time = tick()
		local bobX = math.sin(time * 8) * VIEWMODEL_CONFIG.bobIntensity * (walkSpeed / 16)
		local bobY = math.abs(math.sin(time * 16)) * VIEWMODEL_CONFIG.bobIntensity * (walkSpeed / 16)

		self.bobOffset = CFrame.new(bobX, bobY, 0)
	else
		-- Smooth return to center
		self.bobOffset = self.bobOffset:Lerp(CFrame.new(), deltaTime * 5)
	end
end

function ViewmodelSystem:_updateViewmodelPosition(): ()
	local cameraCFrame = self.camera.CFrame

	-- Combine all offsets
	local totalOffset = self.baseOffset * self.swayOffset * self.bobOffset * self.aimOffset

	-- Position arms
	if self.leftArm then
		local leftOffset = totalOffset * (self.leftArmOffset or CFrame.new())
		self.leftArm.CFrame = cameraCFrame * leftOffset
	end

	if self.rightArm then
		local rightOffset = totalOffset * (self.rightArmOffset or CFrame.new())
		self.rightArm.CFrame = cameraCFrame * rightOffset
	end

	-- Position weapon
	if self.weaponModel then
		local weaponOffset = totalOffset * VIEWMODEL_CONFIG.weaponOffset * VIEWMODEL_CONFIG.weaponRotation
		self.weaponModel.CFrame = cameraCFrame * weaponOffset
	end
end

-- ============================================================================
-- EXTERNAL INTERFACE
-- ============================================================================

function ViewmodelSystem:setCharacter(character: Model): ()
	self.character = character

	-- Update arm color based on character
	if character:FindFirstChild("Body Colors") then
		local bodyColors = character["Body Colors"]
		local skinColor = bodyColors.LeftArmColor3

		if self.leftArm then
			self.leftArm.Color = skinColor
		end
		if self.rightArm then
			self.rightArm.Color = skinColor
		end
	end
end

function ViewmodelSystem:updateCamera(cameraCFrame: CFrame): ()
	-- Camera is automatically updated in _update loop
end

function ViewmodelSystem:setAiming(aiming: boolean): ()
	self.isAiming = aiming

	-- Animate to ADS position
	local targetAimOffset = aiming and CFrame.new(0, 0, 0.5) or CFrame.new()

	-- Smooth transition (this would be better with TweenService in a real implementation)
	local startOffset = self.aimOffset
	local startTime = tick()

	local connection
	connection = RunService.Heartbeat:Connect(function()
		local elapsed = tick() - startTime
		local progress = math.min(elapsed / VIEWMODEL_CONFIG.aimTransitionTime, 1)

		self.aimOffset = startOffset:Lerp(targetAimOffset, progress)

		if progress >= 1 then
			connection:Disconnect()
		end
	end)
end

function ViewmodelSystem:onWeaponFired(fireResult: any): ()
	-- Add muzzle flash effect or other visual feedback
	-- This would be implemented based on the weapon's effects
end

function ViewmodelSystem:onWeaponReloaded(reloadResult: any): ()
	-- Add reload visual feedback
	-- This would be implemented based on the weapon's reload animation
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function ViewmodelSystem:cleanup(): ()
	-- Disconnect all connections
	for _, connection in pairs(self.connections) do
		connection:Disconnect()
	end

	-- Destroy viewmodel
	if self.viewmodel then
		self.viewmodel:Destroy()
	end

	-- Clear references
	self.character = nil
	self.camera = nil
	self.currentWeapon = nil
end

return ViewmodelSystem
