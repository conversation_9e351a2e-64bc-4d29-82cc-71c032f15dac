--!strict
--[[
	Gun System Framework - FPS Camera System
	
	This module manages the first-person camera for R6 blocky characters:
	- FPS camera controls and positioning
	- Recoil and sway effects
	- ADS (Aim Down Sights) transitions
	- Camera shake and effects
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local Workspace = game:GetService("Workspace")

-- ============================================================================
-- FPS CAMERA SYSTEM
-- ============================================================================

local FPSCamera = {}
FPSCamera.__index = FPSCamera

-- Camera configuration
local CAMERA_CONFIG = {
	-- Basic settings
	fieldOfView = 70,
	adsFieldOfView = 50,
	mouseSensitivity = 1.0,

	-- Recoil settings
	recoilDecayRate = 5.0,
	maxRecoilAngle = 15.0,

	-- Sway settings
	swayIntensity = 0.5,
	swaySpeed = 2.0,

	-- ADS settings
	adsTransitionTime = 0.3,
	adsSensitivityMultiplier = 0.5,

	-- Character settings for R6
	headOffset = Vector3.new(0, 1.5, 0), -- R6 head position
	hideCharacterInFirstPerson = true,
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function FPSCamera.new(): FPSCamera
	local self = setmetatable({}, FPSCamera)

	-- State
	self.camera = Workspace.CurrentCamera
	self.character = nil
	self.humanoid = nil
	self.head = nil
	self.currentWeapon = nil

	-- Camera state
	self.isAiming = false
	self.baseCFrame = CFrame.new()
	self.recoilOffset = CFrame.new()
	self.swayOffset = CFrame.new()
	self.currentFOV = CAMERA_CONFIG.fieldOfView

	-- Mouse state
	self.mouseDelta = Vector2.new()
	self.cameraAngles = Vector2.new()

	-- Events
	self.onCameraUpdate = Instance.new("BindableEvent").Event

	-- Connections
	self.connections = {}

	-- Initialize
	self:_initialize()

	return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function FPSCamera:_initialize(): ()
	-- Set camera type
	self.camera.CameraType = Enum.CameraType.Scriptable

	-- Lock mouse to center
	UserInputService.MouseBehavior = Enum.MouseBehavior.LockCenter

	-- Set up camera update loop
	self.connections.heartbeat = RunService.Heartbeat:Connect(function(deltaTime)
		self:_updateCamera(deltaTime)
	end)

	-- Set up mouse input
	self.connections.mouseMoved = UserInputService.InputChanged:Connect(function(input)
		if input.UserInputType == Enum.UserInputType.MouseMovement then
			self:_handleMouseMovement(input)
		end
	end)

	-- Get initial character
	local localPlayer = Players.LocalPlayer
	if localPlayer.Character then
		self:setCharacter(localPlayer.Character)
	end
end

-- ============================================================================
-- CHARACTER MANAGEMENT
-- ============================================================================

function FPSCamera:setCharacter(character: Model): ()
	self.character = character
	self.humanoid = character:WaitForChild("Humanoid")
	self.head = character:WaitForChild("Head")

	-- Hide character parts in first person
	if CAMERA_CONFIG.hideCharacterInFirstPerson then
		self:_hideCharacterParts()
	end

	-- Set initial camera position
	self:_updateCameraPosition()
end

function FPSCamera:_hideCharacterParts(): ()
	if not self.character then
		return
	end

	-- Hide body parts but keep head for collision
	local partsToHide = {"Torso", "Left Arm", "Right Arm", "Left Leg", "Right Leg"}

	for _, partName in ipairs(partsToHide) do
		local part = self.character:FindFirstChild(partName)
		if part then
			part.Transparency = 1

			-- Hide accessories on these parts
			for _, child in ipairs(part:GetChildren()) do
				if child:IsA("Accessory") or child:IsA("Hat") then
					for _, accessoryPart in ipairs(child:GetDescendants()) do
						if accessoryPart:IsA("BasePart") then
							accessoryPart.Transparency = 1
						end
					end
				end
			end
		end
	end

	-- Make head semi-transparent
	if self.head then
		self.head.Transparency = 0.5
	end
end

-- ============================================================================
-- CAMERA CONTROLS
-- ============================================================================

function FPSCamera:_handleMouseMovement(input: InputObject): ()
	-- Get mouse delta
	self.mouseDelta = Vector2.new(input.Delta.X, input.Delta.Y)

	-- Apply sensitivity
	local sensitivity = CAMERA_CONFIG.mouseSensitivity
	if self.isAiming then
		sensitivity *= CAMERA_CONFIG.adsSensitivityMultiplier
	end

	-- Update camera angles
	self.cameraAngles = Vector2.new(
		self.cameraAngles.X - self.mouseDelta.Y * sensitivity * 0.01,
		self.cameraAngles.Y + self.mouseDelta.X * sensitivity * 0.01
	)

	-- Clamp vertical angle
	self.cameraAngles = Vector2.new(
		math.clamp(self.cameraAngles.X, math.rad(-80), math.rad(80)),
		self.cameraAngles.Y
	)
end

function FPSCamera:_updateCamera(deltaTime: number): ()
	if not self.head then
		return
	end

	-- Update camera position and rotation
	self:_updateCameraPosition()

	-- Update recoil decay
	self:_updateRecoilDecay(deltaTime)

	-- Update sway
	self:_updateSway(deltaTime)

	-- Apply all offsets
	self:_applyCameraOffsets()

	-- Fire camera update event
	self.onCameraUpdate:Fire(self.camera.CFrame)
end

function FPSCamera:_updateCameraPosition(): ()
	if not self.head then
		return
	end

	-- Get head position
	local headPosition = self.head.Position + CAMERA_CONFIG.headOffset

	-- Create base camera CFrame
	self.baseCFrame = CFrame.new(headPosition) * CFrame.Angles(self.cameraAngles.X, self.cameraAngles.Y, 0)
end

function FPSCamera:_updateRecoilDecay(deltaTime: number): ()
	-- Decay recoil over time
	local decayRate = CAMERA_CONFIG.recoilDecayRate * deltaTime
	self.recoilOffset = self.recoilOffset:Lerp(CFrame.new(), decayRate)
end

function FPSCamera:_updateSway(deltaTime: number): ()
	-- Simple breathing sway effect
	local time = tick()
	local swayX = math.sin(time * CAMERA_CONFIG.swaySpeed) * CAMERA_CONFIG.swayIntensity * 0.001
	local swayY = math.cos(time * CAMERA_CONFIG.swaySpeed * 0.7) * CAMERA_CONFIG.swayIntensity * 0.001

	self.swayOffset = CFrame.new(swayX, swayY, 0)
end

function FPSCamera:_applyCameraOffsets(): ()
	-- Combine all camera offsets
	local finalCFrame = self.baseCFrame * self.recoilOffset * self.swayOffset

	-- Apply to camera
	self.camera.CFrame = finalCFrame
	self.camera.FieldOfView = self.currentFOV
end

-- ============================================================================
-- WEAPON INTEGRATION
-- ============================================================================

function FPSCamera:setWeapon(weapon: any): ()
	self.currentWeapon = weapon

	-- Adjust camera settings based on weapon
	if weapon.category == "SniperRifle" then
		CAMERA_CONFIG.adsFieldOfView = 30
	elseif weapon.category == "Pistol" then
		CAMERA_CONFIG.adsFieldOfView = 60
	else
		CAMERA_CONFIG.adsFieldOfView = 50
	end
end

function FPSCamera:clearWeapon(): ()
	self.currentWeapon = nil

	-- Reset to default settings
	CAMERA_CONFIG.adsFieldOfView = 50
end

function FPSCamera:applyRecoil(recoilVector: { X: number, Y: number, Z: number }): ()
	-- Convert recoil vector to camera rotation
	local recoilX = math.rad(recoilVector.Y * 2) -- Vertical recoil
	local recoilY = math.rad(recoilVector.X * 1) -- Horizontal recoil

	-- Clamp recoil to maximum
	recoilX = math.clamp(recoilX, -math.rad(CAMERA_CONFIG.maxRecoilAngle), math.rad(CAMERA_CONFIG.maxRecoilAngle))
	recoilY = math.clamp(recoilY, -math.rad(CAMERA_CONFIG.maxRecoilAngle), math.rad(CAMERA_CONFIG.maxRecoilAngle))

	-- Apply recoil to camera angles
	self.cameraAngles = Vector2.new(
		self.cameraAngles.X + recoilX,
		self.cameraAngles.Y + recoilY
	)

	-- Also add to recoil offset for immediate effect
	self.recoilOffset = self.recoilOffset * CFrame.Angles(recoilX * 0.5, recoilY * 0.5, 0)
end

function FPSCamera:setAiming(aiming: boolean): ()
	self.isAiming = aiming

	-- Transition FOV
	local targetFOV = aiming and CAMERA_CONFIG.adsFieldOfView or CAMERA_CONFIG.fieldOfView
	self:_transitionFOV(targetFOV)
end

function FPSCamera:_transitionFOV(targetFOV: number): ()
	-- Simple FOV transition (could be improved with TweenService)
	local startFOV = self.currentFOV
	local startTime = tick()

	local connection
	connection = RunService.Heartbeat:Connect(function()
		local elapsed = tick() - startTime
		local progress = math.min(elapsed / CAMERA_CONFIG.adsTransitionTime, 1)

		-- Ease out transition
		local easedProgress = 1 - math.pow(1 - progress, 2)
		self.currentFOV = startFOV + (targetFOV - startFOV) * easedProgress

		if progress >= 1 then
			connection:Disconnect()
		end
	end)
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function FPSCamera:getCameraCFrame(): CFrame
	return self.camera.CFrame
end

function FPSCamera:getFieldOfView(): number
	return self.currentFOV
end

function FPSCamera:setMouseSensitivity(sensitivity: number): ()
	CAMERA_CONFIG.mouseSensitivity = sensitivity
end

function FPSCamera:getMouseSensitivity(): number
	return CAMERA_CONFIG.mouseSensitivity
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function FPSCamera:cleanup(): ()
	-- Disconnect all connections
	for _, connection in pairs(self.connections) do
		connection:Disconnect()
	end

	-- Reset camera
	self.camera.CameraType = Enum.CameraType.Custom
	UserInputService.MouseBehavior = Enum.MouseBehavior.Default

	-- Show character parts
	if self.character and CAMERA_CONFIG.hideCharacterInFirstPerson then
		self:_showCharacterParts()
	end

	-- Clear references
	self.character = nil
	self.humanoid = nil
	self.head = nil
	self.currentWeapon = nil
	self.connections = nil
end

function FPSCamera:_showCharacterParts(): ()
	if not self.character then
		return
	end

	-- Show all body parts
	for _, part in ipairs(self.character:GetChildren()) do
		if part:IsA("BasePart") then
			part.Transparency = 0

			-- Show accessories
			for _, child in ipairs(part:GetChildren()) do
				if child:IsA("Accessory") or child:IsA("Hat") then
					for _, accessoryPart in ipairs(child:GetDescendants()) do
						if accessoryPart:IsA("BasePart") then
							accessoryPart.Transparency = 0
						end
					end
				end
			end
		end
	end
end

return FPSCamera
