--!strict
--[[
	Gun System Framework - Player Weapon Manager
	
	This module manages player weapon states on the server:
	- Player weapon inventory
	- Weapon state synchronization
	- Ammunition tracking
	- Weapon permissions and restrictions
]]

local Players = game:GetService("Players")
local DataStoreService = game:GetService("DataStoreService")

-- ============================================================================
-- PLAYER WEAPON MANAGER
-- ============================================================================

local PlayerWeaponManager = {}
PlayerWeaponManager.__index = PlayerWeaponManager

-- Default player data structure
local DEFAULT_PLAYER_DATA = {
	weapons = {},
	currentWeapon = nil,
	ammunition = {},
	statistics = {
		shotsFired = 0,
		shotsHit = 0,
		kills = 0,
		deaths = 0,
		accuracy = 0,
	},
	preferences = {
		mouseSensitivity = 1.0,
		fieldOfView = 70,
		crosshairStyle = "default",
	},
	permissions = {
		canUseWeapons = true,
		allowedWeaponTypes = {"AssaultRifle", "Pistol", "SniperRifle", "Shotgun"},
		maxWeapons = 5,
	},
}

-- ============================================================================
-- CONSTRUCTOR
-- ============================================================================

function PlayerWeaponManager.new(config: { [string]: any }): PlayerWeaponManager
	local self = setmetatable({}, PlayerWeaponManager)

	-- Configuration
	self.config = config or {}

	-- Player data storage
	self.playerData = {} -- [Player] = PlayerData

	-- Data persistence
	self.dataStore = nil
	self.enableDataPersistence = false

	-- Statistics tracking
	self.globalStats = {
		totalPlayers = 0,
		totalWeapons = 0,
		totalShots = 0,
	}

	-- Initialize
	self:_initialize()

	return self
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function PlayerWeaponManager:_initialize(): ()
	-- Set up data store (optional)
	self:_setupDataStore()

	-- Set up player connections
	self:_setupPlayerConnections()

	print("[PlayerWeaponManager] Player weapon manager initialized")
end

function PlayerWeaponManager:_setupDataStore(): ()
	-- Set up data store for persistence (optional)
	local success, dataStore = pcall(function()
		return DataStoreService:GetDataStore("GSF_PlayerData")
	end)

	if success then
		self.dataStore = dataStore
		self.enableDataPersistence = true
		print("[PlayerWeaponManager] Data persistence enabled")
	else
		warn("[PlayerWeaponManager] Failed to set up data store, persistence disabled")
	end
end

function PlayerWeaponManager:_setupPlayerConnections(): ()
	-- Handle existing players
	for _, player in ipairs(Players:GetPlayers()) do
		self:addPlayer(player)
	end

	-- Handle new players
	Players.PlayerAdded:Connect(function(player)
		self:addPlayer(player)
	end)

	-- Handle players leaving
	Players.PlayerRemoving:Connect(function(player)
		self:removePlayer(player)
	end)
end

-- ============================================================================
-- PLAYER MANAGEMENT
-- ============================================================================

function PlayerWeaponManager:addPlayer(player: Player): ()
	print(`[PlayerWeaponManager] Adding player: {player.Name}`)

	-- Load player data
	local playerData = self:_loadPlayerData(player)
	self.playerData[player] = playerData

	-- Update global stats
	self.globalStats.totalPlayers += 1

	-- Give default weapons if none exist
	if #playerData.weapons == 0 then
		self:_giveDefaultWeapons(player)
	end
end

function PlayerWeaponManager:removePlayer(player: Player): ()
	print(`[PlayerWeaponManager] Removing player: {player.Name}`)

	-- Save player data
	self:_savePlayerData(player)

	-- Clean up
	self.playerData[player] = nil
	self.globalStats.totalPlayers -= 1
end

function PlayerWeaponManager:_loadPlayerData(player: Player): any
	local playerData = table.clone(DEFAULT_PLAYER_DATA)

	if self.enableDataPersistence and self.dataStore then
		local success, savedData = pcall(function()
			return self.dataStore:GetAsync(`Player_{player.UserId}`)
		end)

		if success and savedData then
			-- Merge saved data with defaults
			for key, value in pairs(savedData) do
				if playerData[key] then
					playerData[key] = value
				end
			end
			print(`[PlayerWeaponManager] Loaded data for {player.Name}`)
		else
			print(`[PlayerWeaponManager] No saved data for {player.Name}, using defaults`)
		end
	end

	return playerData
end

function PlayerWeaponManager:_savePlayerData(player: Player): ()
	local playerData = self.playerData[player]
	if not playerData or not self.enableDataPersistence or not self.dataStore then
		return
	end

	local success, error = pcall(function()
		self.dataStore:SetAsync(`Player_{player.UserId}`, playerData)
	end)

	if success then
		print(`[PlayerWeaponManager] Saved data for {player.Name}`)
	else
		warn(`[PlayerWeaponManager] Failed to save data for {player.Name}: {error}`)
	end
end

function PlayerWeaponManager:_giveDefaultWeapons(player: Player): ()
	-- Give default starter weapons
	local defaultWeapons = {
		{
			id = `{player.Name}_m4a1_default`,
			name = "M4A1",
			category = "AssaultRifle",
			components = {},
			ammunition = 30,
		},
		{
			id = `{player.Name}_glock17_default`,
			name = "Glock17",
			category = "Pistol",
			components = {},
			ammunition = 17,
		},
	}

	local playerData = self.playerData[player]
	for _, weapon in ipairs(defaultWeapons) do
		table.insert(playerData.weapons, weapon)
		self.globalStats.totalWeapons += 1
	end

	-- Set first weapon as current
	playerData.currentWeapon = defaultWeapons[1]

	print(`[PlayerWeaponManager] Gave default weapons to {player.Name}`)
end

-- ============================================================================
-- WEAPON MANAGEMENT
-- ============================================================================

function PlayerWeaponManager:getPlayerWeapons(player: Player): { any }
	local playerData = self.playerData[player]
	return playerData and playerData.weapons or {}
end

function PlayerWeaponManager:getPlayerCurrentWeapon(player: Player): any?
	local playerData = self.playerData[player]
	return playerData and playerData.currentWeapon
end

function PlayerWeaponManager:setPlayerCurrentWeapon(player: Player, weaponId: string): boolean
	local playerData = self.playerData[player]
	if not playerData then
		return false
	end

	-- Find weapon in player's inventory
	for _, weapon in ipairs(playerData.weapons) do
		if weapon.id == weaponId then
			playerData.currentWeapon = weapon
			print(`[PlayerWeaponManager] {player.Name} equipped {weapon.name}`)
			return true
		end
	end

	warn(`[PlayerWeaponManager] Weapon {weaponId} not found in {player.Name}'s inventory`)
	return false
end

function PlayerWeaponManager:addWeaponToPlayer(player: Player, weaponData: any): boolean
	local playerData = self.playerData[player]
	if not playerData then
		return false
	end

	-- Check permissions
	if not self:_canPlayerUseWeapon(player, weaponData) then
		return false
	end

	-- Check inventory limit
	if #playerData.weapons >= playerData.permissions.maxWeapons then
		warn(`[PlayerWeaponManager] {player.Name} has reached weapon limit`)
		return false
	end

	-- Add weapon
	table.insert(playerData.weapons, weaponData)
	self.globalStats.totalWeapons += 1

	print(`[PlayerWeaponManager] Added {weaponData.name} to {player.Name}'s inventory`)
	return true
end

function PlayerWeaponManager:removeWeaponFromPlayer(player: Player, weaponId: string): boolean
	local playerData = self.playerData[player]
	if not playerData then
		return false
	end

	-- Find and remove weapon
	for i, weapon in ipairs(playerData.weapons) do
		if weapon.id == weaponId then
			table.remove(playerData.weapons, i)
			self.globalStats.totalWeapons -= 1

			-- Clear current weapon if it was removed
			if playerData.currentWeapon and playerData.currentWeapon.id == weaponId then
				playerData.currentWeapon = playerData.weapons[1] -- Set to first weapon or nil
			end

			print(`[PlayerWeaponManager] Removed {weapon.name} from {player.Name}'s inventory`)
			return true
		end
	end

	return false
end

function PlayerWeaponManager:_canPlayerUseWeapon(player: Player, weaponData: any): boolean
	local playerData = self.playerData[player]
	if not playerData then
		return false
	end

	-- Check general weapon permission
	if not playerData.permissions.canUseWeapons then
		return false
	end

	-- Check weapon type permission
	local allowedTypes = playerData.permissions.allowedWeaponTypes
	if not table.find(allowedTypes, weaponData.category) then
		return false
	end

	return true
end

-- ============================================================================
-- AMMUNITION MANAGEMENT
-- ============================================================================

function PlayerWeaponManager:getPlayerAmmunition(player: Player, ammunitionType: string?): any
	local playerData = self.playerData[player]
	if not playerData then
		return 0
	end

	if ammunitionType then
		return playerData.ammunition[ammunitionType] or 0
	else
		return playerData.ammunition
	end
end

function PlayerWeaponManager:setPlayerAmmunition(player: Player, ammunitionType: string, amount: number): ()
	local playerData = self.playerData[player]
	if not playerData then
		return
	end

	playerData.ammunition[ammunitionType] = math.max(0, amount)
end

function PlayerWeaponManager:addPlayerAmmunition(player: Player, ammunitionType: string, amount: number): ()
	local playerData = self.playerData[player]
	if not playerData then
		return
	end

	local currentAmount = playerData.ammunition[ammunitionType] or 0
	playerData.ammunition[ammunitionType] = currentAmount + amount
end

function PlayerWeaponManager:consumePlayerAmmunition(player: Player, ammunitionType: string, amount: number): boolean
	local playerData = self.playerData[player]
	if not playerData then
		return false
	end

	local currentAmount = playerData.ammunition[ammunitionType] or 0
	if currentAmount >= amount then
		playerData.ammunition[ammunitionType] = currentAmount - amount
		return true
	end

	return false
end

-- ============================================================================
-- STATISTICS TRACKING
-- ============================================================================

function PlayerWeaponManager:updatePlayerStatistics(player: Player, statType: string, value: number): ()
	local playerData = self.playerData[player]
	if not playerData then
		return
	end

	if playerData.statistics[statType] ~= nil then
		playerData.statistics[statType] += value

		-- Update accuracy
		if statType == "shotsFired" or statType == "shotsHit" then
			local shots = playerData.statistics.shotsFired
			local hits = playerData.statistics.shotsHit
			playerData.statistics.accuracy = shots > 0 and (hits / shots) * 100 or 0
		end

		-- Update global stats
		if statType == "shotsFired" then
			self.globalStats.totalShots += value
		end
	end
end

function PlayerWeaponManager:getPlayerStatistics(player: Player): any?
	local playerData = self.playerData[player]
	return playerData and playerData.statistics
end

function PlayerWeaponManager:getGlobalStatistics(): any
	return self.globalStats
end

-- ============================================================================
-- PREFERENCES MANAGEMENT
-- ============================================================================

function PlayerWeaponManager:getPlayerPreferences(player: Player): any?
	local playerData = self.playerData[player]
	return playerData and playerData.preferences
end

function PlayerWeaponManager:setPlayerPreference(player: Player, preference: string, value: any): boolean
	local playerData = self.playerData[player]
	if not playerData or playerData.preferences[preference] == nil then
		return false
	end

	playerData.preferences[preference] = value
	return true
end

-- ============================================================================
-- PERMISSIONS MANAGEMENT
-- ============================================================================

function PlayerWeaponManager:getPlayerPermissions(player: Player): any?
	local playerData = self.playerData[player]
	return playerData and playerData.permissions
end

function PlayerWeaponManager:setPlayerPermission(player: Player, permission: string, value: any): boolean
	local playerData = self.playerData[player]
	if not playerData or playerData.permissions[permission] == nil then
		return false
	end

	playerData.permissions[permission] = value
	return true
end

function PlayerWeaponManager:canPlayerUseWeapons(player: Player): boolean
	local playerData = self.playerData[player]
	return playerData and playerData.permissions.canUseWeapons or false
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function PlayerWeaponManager:getPlayerData(player: Player): any?
	return self.playerData[player]
end

function PlayerWeaponManager:getAllPlayerData(): { [Player]: any }
	return self.playerData
end

function PlayerWeaponManager:saveAllPlayerData(): ()
	for player, _ in pairs(self.playerData) do
		self:_savePlayerData(player)
	end
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function PlayerWeaponManager:cleanup(): ()
	-- Save all player data
	self:saveAllPlayerData()

	-- Clear data
	self.playerData = {}

	-- Reset stats
	self.globalStats = {
		totalPlayers = 0,
		totalWeapons = 0,
		totalShots = 0,
	}

	print("[PlayerWeaponManager] Player weapon manager cleaned up")
end

return PlayerWeaponManager
