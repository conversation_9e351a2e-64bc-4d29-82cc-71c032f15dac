--!strict
--[[
	Gun System Framework - Server Core
	
	This module initializes the server-side GSF systems:
	- Authoritative weapon state management
	- Hit registration and validation
	- Anti-cheat systems
	- Player weapon management
	- Server-side ballistics validation
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Import GSF modules
local GSF = require(ReplicatedStorage.GSF.Core.GSF_Core)
local WeaponValidator = require(script.Parent.GSF_WeaponValidator)
local HitRegistration = require(script.Parent.GSF_HitRegistration)
local AntiCheat = require(script.Parent.GSF_AntiCheat)
local PlayerWeaponManager = require(script.Parent.GSF_PlayerWeaponManager)

-- ============================================================================
-- SERVER CORE SYSTEM
-- ============================================================================

local ServerCore = {}
ServerCore.__index = ServerCore

-- Server state
local isInitialized = false
local playerWeapons = {} -- [Player] = WeaponData
local serverSystems = {}

-- Server configuration
local SERVER_CONFIG = {
	-- Validation settings
	enableStrictValidation = true,
	maxFireRate = 1200, -- RPM
	maxRange = 2000, -- studs

	-- Anti-cheat settings
	enableAntiCheat = true,
	maxVelocityDeviation = 0.1, -- 10% deviation allowed
	maxPositionDeviation = 5, -- studs

	-- Performance settings
	maxPlayersWithWeapons = 50,
	weaponUpdateRate = 20, -- Hz
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function ServerCore.initialize(): boolean
	if isInitialized then
		warn("[GSF Server] Already initialized")
		return false
	end

	print("[GSF Server] Initializing server-side systems...")

	-- Initialize core GSF
	if not GSF.isInitialized then
		GSF.initialize()
	end

	-- Initialize server systems
	serverSystems.validator = WeaponValidator.new(SERVER_CONFIG)
	serverSystems.hitRegistration = HitRegistration.new(SERVER_CONFIG)
	serverSystems.antiCheat = AntiCheat.new(SERVER_CONFIG)
	serverSystems.playerManager = PlayerWeaponManager.new(SERVER_CONFIG)

	-- Set up player connections
	ServerCore._setupPlayerConnections()

	-- Set up remote events
	ServerCore._setupRemoteEvents()

	-- Start server update loop
	ServerCore._startUpdateLoop()

	isInitialized = true
	print("[GSF Server] Server-side systems initialized successfully")
	return true
end

function ServerCore._setupPlayerConnections(): ()
	-- Handle player joining
	Players.PlayerAdded:Connect(function(player)
		ServerCore._onPlayerAdded(player)
	end)

	-- Handle player leaving
	Players.PlayerRemoving:Connect(function(player)
		ServerCore._onPlayerRemoving(player)
	end)

	-- Handle existing players
	for _, player in ipairs(Players:GetPlayers()) do
		ServerCore._onPlayerAdded(player)
	end
end

function ServerCore._setupRemoteEvents(): ()
	-- Create remote events folder
	local remoteEvents = Instance.new("Folder")
	remoteEvents.Name = "GSF_RemoteEvents"
	remoteEvents.Parent = ReplicatedStorage

	-- Weapon fire event
	local fireWeaponEvent = Instance.new("RemoteEvent")
	fireWeaponEvent.Name = "FireWeapon"
	fireWeaponEvent.Parent = remoteEvents

	fireWeaponEvent.OnServerEvent:Connect(function(player, weaponData, origin, direction, timestamp)
		ServerCore._handleWeaponFire(player, weaponData, origin, direction, timestamp)
	end)

	-- Weapon reload event
	local reloadWeaponEvent = Instance.new("RemoteEvent")
	reloadWeaponEvent.Name = "ReloadWeapon"
	reloadWeaponEvent.Parent = remoteEvents

	reloadWeaponEvent.OnServerEvent:Connect(function(player, weaponData, timestamp)
		ServerCore._handleWeaponReload(player, weaponData, timestamp)
	end)

	-- Weapon equip event
	local equipWeaponEvent = Instance.new("RemoteEvent")
	equipWeaponEvent.Name = "EquipWeapon"
	equipWeaponEvent.Parent = remoteEvents

	equipWeaponEvent.OnServerEvent:Connect(function(player, weaponData)
		ServerCore._handleWeaponEquip(player, weaponData)
	end)

	-- Hit registration event
	local hitRegistrationEvent = Instance.new("RemoteEvent")
	hitRegistrationEvent.Name = "HitRegistration"
	hitRegistrationEvent.Parent = remoteEvents

	hitRegistrationEvent.OnServerEvent:Connect(function(player, hitData)
		ServerCore._handleHitRegistration(player, hitData)
	end)
end

function ServerCore._startUpdateLoop(): ()
	-- Server update loop for weapon state synchronization
	RunService.Heartbeat:Connect(function(deltaTime)
		ServerCore._updateWeaponStates(deltaTime)
	end)
end

-- ============================================================================
-- PLAYER MANAGEMENT
-- ============================================================================

function ServerCore._onPlayerAdded(player: Player): ()
	print(`[GSF Server] Player {player.Name} joined, initializing weapon systems`)

	-- Initialize player weapon data
	playerWeapons[player] = {
		currentWeapon = nil,
		weaponHistory = {},
		lastFireTime = 0,
		lastReloadTime = 0,
		suspicionLevel = 0,
	}

	-- Register with player manager
	if serverSystems.playerManager then
		serverSystems.playerManager:addPlayer(player)
	end
end

function ServerCore._onPlayerRemoving(player: Player): ()
	print(`[GSF Server] Player {player.Name} leaving, cleaning up weapon systems`)

	-- Clean up player data
	playerWeapons[player] = nil

	-- Unregister from systems
	if serverSystems.playerManager then
		serverSystems.playerManager:removePlayer(player)
	end

	if serverSystems.antiCheat then
		serverSystems.antiCheat:clearPlayerData(player)
	end
end

-- ============================================================================
-- WEAPON EVENT HANDLERS
-- ============================================================================

function ServerCore._handleWeaponFire(player: Player, weaponData: any, origin: Vector3, direction: Vector3, timestamp: number): ()
	-- Validate player
	if not playerWeapons[player] then
		warn(`[GSF Server] Invalid player fire request: {player.Name}`)
		return
	end

	-- Validate weapon data
	local validationResult = serverSystems.validator:validateWeaponFire(player, weaponData, origin, direction, timestamp)
	if not validationResult.valid then
		warn(`[GSF Server] Invalid weapon fire from {player.Name}: {validationResult.reason}`)

		-- Report to anti-cheat
		if serverSystems.antiCheat then
			serverSystems.antiCheat:reportSuspiciousActivity(player, "invalid_fire", validationResult.reason)
		end
		return
	end

	-- Process the fire
	local fireResult = ServerCore._processWeaponFire(player, weaponData, origin, direction, timestamp)

	-- Handle hit registration
	if fireResult.hit then
		serverSystems.hitRegistration:processHit(player, fireResult)
	end

	-- Update player weapon state
	local playerData = playerWeapons[player]
	playerData.lastFireTime = timestamp
	playerData.currentWeapon = weaponData

	-- Replicate to other clients (excluding the firing player)
	ServerCore._replicateWeaponFire(player, fireResult)
end

function ServerCore._handleWeaponReload(player: Player, weaponData: any, timestamp: number): ()
	-- Validate player
	if not playerWeapons[player] then
		warn(`[GSF Server] Invalid player reload request: {player.Name}`)
		return
	end

	-- Validate reload
	local validationResult = serverSystems.validator:validateWeaponReload(player, weaponData, timestamp)
	if not validationResult.valid then
		warn(`[GSF Server] Invalid weapon reload from {player.Name}: {validationResult.reason}`)
		return
	end

	-- Process the reload
	local reloadResult = ServerCore._processWeaponReload(player, weaponData, timestamp)

	-- Update player weapon state
	local playerData = playerWeapons[player]
	playerData.lastReloadTime = timestamp
	playerData.currentWeapon = weaponData

	-- Replicate to other clients
	ServerCore._replicateWeaponReload(player, reloadResult)
end

function ServerCore._handleWeaponEquip(player: Player, weaponData: any): ()
	-- Validate player
	if not playerWeapons[player] then
		warn(`[GSF Server] Invalid player equip request: {player.Name}`)
		return
	end

	-- Validate weapon
	local validationResult = serverSystems.validator:validateWeaponEquip(player, weaponData)
	if not validationResult.valid then
		warn(`[GSF Server] Invalid weapon equip from {player.Name}: {validationResult.reason}`)
		return
	end

	-- Update player weapon state
	local playerData = playerWeapons[player]
	playerData.currentWeapon = weaponData

	-- Add to weapon history
	table.insert(playerData.weaponHistory, {
		weapon = weaponData,
		equipTime = tick(),
	})

	-- Replicate to other clients
	ServerCore._replicateWeaponEquip(player, weaponData)
end

function ServerCore._handleHitRegistration(player: Player, hitData: any): ()
	-- Validate hit data
	local validationResult = serverSystems.validator:validateHitData(player, hitData)
	if not validationResult.valid then
		warn(`[GSF Server] Invalid hit data from {player.Name}: {validationResult.reason}`)
		return
	end

	-- Process hit with server-side validation
	local hitResult = serverSystems.hitRegistration:validateAndProcessHit(player, hitData)

	if hitResult.valid then
		-- Apply damage or effects
		ServerCore._applyHitEffects(hitResult)

		-- Replicate hit to clients
		ServerCore._replicateHitResult(hitResult)
	end
end

-- ============================================================================
-- WEAPON PROCESSING
-- ============================================================================

function ServerCore._processWeaponFire(player: Player, weaponData: any, origin: Vector3, direction: Vector3, timestamp: number): any
	-- Create weapon instance for server-side processing
	local weapon = GSF.WeaponEntity.create(weaponData)
	if not weapon then
		return { success = false, reason = "Failed to create weapon" }
	end

	-- Perform server-side ballistics calculation
	local fireResult = weapon:fire(origin, direction)

	-- Add server-specific data
	fireResult.player = player
	fireResult.timestamp = timestamp
	fireResult.serverValidated = true

	return fireResult
end

function ServerCore._processWeaponReload(player: Player, weaponData: any, timestamp: number): any
	-- Create weapon instance for server-side processing
	local weapon = GSF.WeaponEntity.create(weaponData)
	if not weapon then
		return { success = false, reason = "Failed to create weapon" }
	end

	-- Perform reload
	local reloadResult = weapon:reload()

	-- Add server-specific data
	reloadResult.player = player
	reloadResult.timestamp = timestamp
	reloadResult.serverValidated = true

	return reloadResult
end

function ServerCore._applyHitEffects(hitResult: any): ()
	-- Apply damage to target
	if hitResult.target and hitResult.target:FindFirstChild("Humanoid") then
		local humanoid = hitResult.target.Humanoid
		humanoid:TakeDamage(hitResult.damage)

		print(`[GSF Server] Applied {hitResult.damage} damage to {hitResult.target.Name}`)
	end
end

-- ============================================================================
-- REPLICATION
-- ============================================================================

function ServerCore._replicateWeaponFire(excludePlayer: Player, fireResult: any): ()
	-- Get remote event
	local remoteEvents = ReplicatedStorage:FindFirstChild("GSF_RemoteEvents")
	if not remoteEvents then
		return
	end

	local fireEvent = remoteEvents:FindFirstChild("WeaponFired")
	if not fireEvent then
		-- Create the event if it doesn't exist
		fireEvent = Instance.new("RemoteEvent")
		fireEvent.Name = "WeaponFired"
		fireEvent.Parent = remoteEvents
	end

	-- Fire to all clients except the original player
	for _, player in ipairs(Players:GetPlayers()) do
		if player ~= excludePlayer then
			fireEvent:FireClient(player, fireResult)
		end
	end
end

function ServerCore._replicateWeaponReload(excludePlayer: Player, reloadResult: any): ()
	-- Similar to fire replication
	local remoteEvents = ReplicatedStorage:FindFirstChild("GSF_RemoteEvents")
	if not remoteEvents then
		return
	end

	local reloadEvent = remoteEvents:FindFirstChild("WeaponReloaded")
	if not reloadEvent then
		reloadEvent = Instance.new("RemoteEvent")
		reloadEvent.Name = "WeaponReloaded"
		reloadEvent.Parent = remoteEvents
	end

	for _, player in ipairs(Players:GetPlayers()) do
		if player ~= excludePlayer then
			reloadEvent:FireClient(player, reloadResult)
		end
	end
end

function ServerCore._replicateWeaponEquip(excludePlayer: Player, weaponData: any): ()
	-- Similar to other replications
	local remoteEvents = ReplicatedStorage:FindFirstChild("GSF_RemoteEvents")
	if not remoteEvents then
		return
	end

	local equipEvent = remoteEvents:FindFirstChild("WeaponEquipped")
	if not equipEvent then
		equipEvent = Instance.new("RemoteEvent")
		equipEvent.Name = "WeaponEquipped"
		equipEvent.Parent = remoteEvents
	end

	for _, player in ipairs(Players:GetPlayers()) do
		if player ~= excludePlayer then
			equipEvent:FireClient(player, excludePlayer, weaponData)
		end
	end
end

function ServerCore._replicateHitResult(hitResult: any): ()
	-- Replicate hit effects to all clients
	local remoteEvents = ReplicatedStorage:FindFirstChild("GSF_RemoteEvents")
	if not remoteEvents then
		return
	end

	local hitEvent = remoteEvents:FindFirstChild("HitRegistered")
	if not hitEvent then
		hitEvent = Instance.new("RemoteEvent")
		hitEvent.Name = "HitRegistered"
		hitEvent.Parent = remoteEvents
	end

	for _, player in ipairs(Players:GetPlayers()) do
		hitEvent:FireClient(player, hitResult)
	end
end

-- ============================================================================
-- UPDATE LOOP
-- ============================================================================

function ServerCore._updateWeaponStates(deltaTime: number): ()
	-- Update weapon states for all players
	for player, weaponData in pairs(playerWeapons) do
		if weaponData.currentWeapon then
			-- Update weapon condition, ammo, etc.
			ServerCore._updatePlayerWeapon(player, weaponData, deltaTime)
		end
	end

	-- Update anti-cheat systems
	if serverSystems.antiCheat then
		serverSystems.antiCheat:update(deltaTime)
	end
end

function ServerCore._updatePlayerWeapon(player: Player, weaponData: any, deltaTime: number): ()
	-- Update weapon state (durability, fouling, etc.)
	if weaponData.currentWeapon then
		-- This would update weapon condition over time
		-- For now, just a placeholder
	end
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function ServerCore.getPlayerWeapon(player: Player): any?
	local playerData = playerWeapons[player]
	return playerData and playerData.currentWeapon
end

function ServerCore.getPlayerWeaponData(player: Player): any?
	return playerWeapons[player]
end

function ServerCore.isPlayerSuspicious(player: Player): boolean
	local playerData = playerWeapons[player]
	return playerData and playerData.suspicionLevel > 5
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function ServerCore.cleanup(): ()
	print("[GSF Server] Cleaning up server systems...")

	-- Cleanup systems
	for _, system in pairs(serverSystems) do
		if system and system.cleanup then
			system:cleanup()
		end
	end

	-- Clear player data
	playerWeapons = {}

	-- Clear state
	isInitialized = false

	print("[GSF Server] Server cleanup completed")
end

-- Initialize when script loads
ServerCore.initialize()

return ServerCore
