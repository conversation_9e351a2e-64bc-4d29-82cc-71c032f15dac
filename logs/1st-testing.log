  16:13:54.640  [GSF Client] Initializing client-side systems...  -  Client - GSF_ClientCore:53
  16:13:54.792  Lighting settings saved!  -  Server
  16:13:54.986  Lighting settings saved!  -  Client
  16:13:58.626  [GSF] Initializing Gun System Framework v1.0.0  -  Client - GSF_Core:71
  16:13:58.626  [EventBus] Event bus initialized  -  Client - GSF_EventBus:415
  16:13:58.626  [ProjectileSystem] Initializing projectile physics system...  -  Client - GSF_ProjectileSystem:588
  16:13:58.626  [AdvancedBallistics] Initializing advanced ballistics system...  -  Client - GSF_Ballistics:60
  16:13:58.626  [AdvancedBallistics] Advanced ballistics system initialized  -  Client - GSF_Ballistics:61
  16:13:58.626  [ProjectileSystem] Projectile system initialized  -  Client - GSF_ProjectileSystem:611
  16:13:58.626  [ConfigurationSystem] Initializing configuration system...  -  Client - GSF_Configuration:267
  16:13:58.627  [ConfigurationSystem] Configuration system initialized  -  Client - GSF_Configuration:275
  16:13:58.627  [EffectsSystem] Initializing visual effects system...  -  Client - GSF_EffectsSystem:51
  16:13:58.628  [EffectsSystem] Created effect pools  -  Client - GSF_EffectsSystem:84
  16:13:58.628  [EffectsSystem] Visual effects system initialized  -  Client - GSF_EffectsSystem:59
  16:13:58.628  [AudioSystem] Initializing weapon audio system...  -  Client - GSF_AudioSystem:68
  16:13:58.628  [AudioSystem] Created audio pools  -  Client - GSF_AudioSystem:104
  16:13:58.628  [AudioSystem] Audio system initialized  -  Client - GSF_AudioSystem:82
  16:13:58.628  [AnimationSystem] Initializing CFrame-based animation system...  -  Client - GSF_AnimationSystem:65
  16:13:58.629  [AnimationSystem] Animation system initialized  -  Client - GSF_AnimationSystem:70
  16:13:58.629  [PerformanceMonitor] Initializing performance monitoring system...  -  Client - GSF_PerformanceMonitor:124
  16:13:58.629  [PerformanceMonitor] Performance monitoring started  -  Client - GSF_PerformanceMonitor:146
  16:13:58.629  [PerformanceMonitor] Performance monitoring system initialized  -  Client - GSF_PerformanceMonitor:132
  16:13:58.629  [ObjectPool] Initializing object pool system...  -  Client - GSF_ObjectPool:311
  16:13:58.629  [ObjectPool] Object pool system initialized  -  Client - GSF_ObjectPool:318
  16:13:58.629  [LODSystem] Initializing Level of Detail system...  -  Client - GSF_LODSystem:122
  16:13:58.629  [LODSystem] LOD system initialized  -  Client - GSF_LODSystem:130
  16:13:58.629  [ObjectPool] Creating standard pools...  -  Client - GSF_ObjectPool:561
  16:13:58.629  [ObjectPool] Created Circular pool 'muzzle_flash' with 15 objects  -  Client - GSF_ObjectPool:128
  16:13:58.629  [ObjectPool] Created Dynamic pool 'shell_casing' with 20 objects  -  Client - GSF_ObjectPool:128
  16:13:58.629  [ObjectPool] Created Fixed pool 'audio_source' with 25 objects  -  Client - GSF_ObjectPool:128
  16:13:58.629  [ObjectPool] Created Circular pool 'impact_effect' with 10 objects  -  Client - GSF_ObjectPool:128
  16:13:58.629  [ObjectPool] Standard pools created  -  Client - GSF_ObjectPool:606
  16:13:58.629  [WeaponCustomizationUI] Initializing weapon customization interface...  -  Client - GSF_WeaponCustomizationUI:52
  16:13:58.630  [WeaponCustomizationUI] Weapon customization UI initialized  -  Client - GSF_WeaponCustomizationUI:60
  16:13:58.630  [GSF] Registering default component factories...  -  Client - GSF_Core:116
  16:13:58.630  [ComponentRegistry] Registered factory for component type: Barrel  -  Client - GSF_ComponentRegistery:66
  16:13:58.630  [ComponentRegistry] Registered factory for component type: Magazine  -  Client - GSF_ComponentRegistery:66
  16:13:58.630  [ComponentRegistry] Registered factory for component type: Sight  -  Client - GSF_ComponentRegistery:66
  16:13:58.630  [ComponentRegistry] Registered factory for component type: Stock  -  Client - GSF_ComponentRegistery:66
  16:13:58.630  [ComponentRegistry] Registered factory for component type: Grip  -  Client - GSF_ComponentRegistery:66
  16:13:58.630  [ComponentRegistry] Registered factory for component type: Suppressor  -  Client - GSF_ComponentRegistery:66
  16:13:58.630  [GSF] Registered 6 default component factories  -  Client - GSF_Core:138
  16:13:58.630  [GSF] Gun System Framework initialized successfully  -  Client - GSF_Core:111
  16:13:58.631  [GSF Client] Client-side systems initialized successfully  -  Client - GSF_ClientCore:78
  16:13:58.665  Fire is not a valid member of RBXScriptSignal  -  Client - GSF_FPSCamera:210
  16:13:58.665  Stack Begin  -  Studio
  16:13:58.665  Script 'Players.1XFPANDAFX1.PlayerScripts.GSF_Client.GSF_FPSCamera', Line 210 - function _updateCamera  -  Studio - GSF_FPSCamera:210
  16:13:58.665  Script 'Players.1XFPANDAFX1.PlayerScripts.GSF_Client.GSF_FPSCamera', Line 98  -  Studio - GSF_FPSCamera:98
  16:13:58.665  Stack End  -  Studio
  16:13:58.791  Fire is not a valid member of RBXScriptSignal  -  Client - GSF_FPSCamera:210
  16:13:58.792  Stack Begin  -  Studio
  16:13:58.792  Script 'Players.1XFPANDAFX1.PlayerScripts.GSF_Client.GSF_FPSCamera', Line 210 - function _updateCamera  -  Studio - GSF_FPSCamera:210
  16:13:58.792  Script 'Players.1XFPANDAFX1.PlayerScripts.GSF_Client.GSF_FPSCamera', Line 98  -  Studio - GSF_FPSCamera:98
  16:13:58.792  Stack End  -  Studio
